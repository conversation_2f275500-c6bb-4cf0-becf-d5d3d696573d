import React from 'react';
import { issueSummaryFactory } from '@shape-construction/api/factories/issues';
import { sharedCursorPaginationMetaFactory } from '@shape-construction/api/factories/sharedCursorPagination';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler } from '@shape-construction/api/handlers-factories/projects/data-book';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/src/mock-handlers/getApiProjectsProjectIdPeopleMockHandler';
import { server } from 'tests/mock-server';
import { getTableData, render, screen } from 'tests/test-utils';
import { IssuesDetailsTable } from './IssuesDetailsTable';

describe('<IssuesDetailsTable />', () => {
  it('displays a table of issues', async () => {
    const teamMember = teamMemberFactory();
    const responsiblePerson = teamMemberFactory({ user: userBasicDetailsFactory({ name: 'Mr. Responsible' }) });
    const issues = [
      issueSummaryFactory({ id: '1' }),
      issueSummaryFactory({ title: 'My issue', qualityScore: 42, observerId: responsiblePerson.id }),
      issueSummaryFactory({ id: '3' }),
    ];
    server.use(
      getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler(() => ({
        entries: issues,
        meta: sharedCursorPaginationMetaFactory({ total: issues.length }),
      })),
      getApiProjectsProjectIdPeopleMockHandler([teamMember, responsiblePerson])
    );

    render(<IssuesDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

    const issueRows = (await screen.findAllByRole('row')).filter((row) => row.closest('tbody'));
    expect(issueRows).toHaveLength(3);
    const tableData = getTableData(await screen.findByRole('table'));
    expect(tableData[1]).toMatchObject([
      [expect.stringMatching(/.*\.headers\.title$/), 'My issue'],
      [expect.stringMatching(/.*\.headers\.currentResponsible$/), 'Mr. Responsible'],
      [expect.stringMatching(/.*\.headers\.score$/), 'dataBook.page.heatmapDashboard.heatmap.qualityLabel.good'],
    ]);
  });

  describe('when the total number of results is 0', () => {
    it('renders the empty state', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler(() => ({
          entries: [],
          meta: sharedCursorPaginationMetaFactory({ total: 0 }),
        }))
      );

      render(<IssuesDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByText('dataBook.page.heatmapDashboard.performanceDetails.issuesTable.emptyState.title')
      ).toBeInTheDocument();
      expect(
        await screen.findByText('dataBook.page.heatmapDashboard.performanceDetails.issuesTable.emptyState.body')
      ).toBeInTheDocument();
    });
  });

  describe('when there is no next page', () => {
    it('does not render the next button', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler(() => ({
          entries: [issueSummaryFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasPreviousPage: true,
            hasNextPage: false,
          }),
        }))
      );

      render(<IssuesDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByRole('button', {
          name: 'table.cursorPagination.next',
        })
      ).not.toBeEnabled();
    });
  });

  describe('when there is next page', () => {
    it('renders the next button', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler(() => ({
          entries: [issueSummaryFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasNextPage: true,
            lastEntryCursor: 'last-cursor',
          }),
        }))
      );

      render(<IssuesDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByRole('button', {
          name: 'table.cursorPagination.next',
        })
      ).toBeEnabled();
    });
  });

  describe('when there is no previous page', () => {
    it('does not render the previous button', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler(() => ({
          entries: [issueSummaryFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasPreviousPage: false,
            hasNextPage: true,
            lastEntryCursor: 'last-cursor',
          }),
        }))
      );

      render(<IssuesDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByRole('button', {
          name: 'table.cursorPagination.previous',
        })
      ).not.toBeEnabled();
    });
  });

  describe('when there is no pagination', () => {
    it('does not render the footer', () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler(() => ({
          entries: [issueSummaryFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasNextPage: false,
            hasPreviousPage: false,
          }),
        }))
      );

      render(<IssuesDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        screen.queryByRole('button', {
          name: 'table.cursorPagination.previous',
        })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', {
          name: 'table.cursorPagination.next',
        })
      ).not.toBeInTheDocument();
    });
  });

  describe('when there is a previous page', () => {
    it('renders the previous button', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsIssuesMockHandler(() => ({
          entries: [issueSummaryFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasPreviousPage: true,
            firstEntryCursor: 'last-cursor',
          }),
        }))
      );

      render(<IssuesDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByRole('button', {
          name: 'table.cursorPagination.previous',
        })
      ).toBeEnabled();
    });
  });
});
