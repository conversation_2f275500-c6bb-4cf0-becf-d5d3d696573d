import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { ScoringInfoListHeader } from './ScoringInfoListHeader';
import { ScoringInfoListItem } from './ScoringInfoListItem';

const AdditionalDetailsNote: React.FC = () => {
  const messages = useMessageGetter('issuesScoring');
  return (
    <div className="flex gap-2 items-start justify-start">
      <InformationCircleIcon className="text-icon-brand" width={32} />
      <p className="text-sm leading-5 font-medium text-accent-blue">{messages('additionalDetails.note')}</p>
    </div>
  );
};

export const IssuesScoringInfo: React.FC = () => {
  const messages = useMessageGetter('issuesScoring');
  return (
    <div className="flex flex-col gap-3 p-2">
      <ScoringInfoListHeader title={messages('basics.title')} />
      <ScoringInfoListItem content={messages('basics.description')} rightSlot="10%" />
      <ScoringInfoListItem content={messages('basics.validAssignee')} rightSlot="10%" />
      <ScoringInfoListItem content={messages('basics.lastActivityWithinFourWeeks')} rightSlot="10%" />

      <ScoringInfoListItem content={<AdditionalDetailsNote />} />

      <Divider orientation="horizontal" />

      <ScoringInfoListHeader title={messages('additionalDetails.title')} />
      <ScoringInfoListItem content={messages('additionalDetails.validLocation')} rightSlot="10%" />
      <ScoringInfoListItem content={messages('additionalDetails.impactIndicated')} rightSlot="10%" />
      <ScoringInfoListItem content={messages('additionalDetails.typeSpecified')} rightSlot="10%" />
      <ScoringInfoListItem content={messages('additionalDetails.dueDateSpecified')} rightSlot="5%" />
      <ScoringInfoListItem content={messages('additionalDetails.discipline')} rightSlot="5%" />
      <ScoringInfoListItem content={messages('additionalDetails.includesAttachment')} rightSlot="20%" />
      <ScoringInfoListItem content={messages('additionalDetails.lastActivityWithinTwoWeeks')} rightSlot="10%" />

      <Divider orientation="horizontal" />

      <ScoringInfoListHeader title={messages('notes.title')} />
      <ScoringInfoListItem content={messages('notes.note1')} />
      <ScoringInfoListItem content={messages('notes.note2')} />
    </div>
  );
};
