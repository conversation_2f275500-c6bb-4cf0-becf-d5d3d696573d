import React from 'react';
import { render, screen, waitFor } from 'tests/test-utils';
import { DataHealthProvider } from '../../data-health-heatmap/DataHealthContext';
import { ScoringInfo } from './ScoringInfo';

describe('<ScoringInfo />', () => {
  describe('when record type is issues', () => {
    it('renders the issues scoring info', async () => {
      render(
        <DataHealthProvider recordType={'issues'} seriesLength={3}>
          <ScoringInfo />
        </DataHealthProvider>
      );

      expect(await screen.findByLabelText('issuesScoring.label')).toBeInTheDocument();
    });
  });

  describe('when record type is shift_reports', () => {
    it('does not render anything', () => {
      const { container } = render(
        <DataHealthProvider recordType={'shift_reports'} seriesLength={3}>
          <ScoringInfo />
        </DataHealthProvider>
      );

      waitFor(() => {
        expect(container).toBeEmptyDOMElement();
      });
    });
  });
});
