import { render, screen } from 'tests/test-utils';
import { ScoringInfoListItem } from './ScoringInfoListItem';

describe('<ScoringInfoListItem />', () => {
  it('renders correct text and value', () => {
    render(<ScoringInfoListItem content="Test text" rightSlot="10%" />);

    expect(screen.getByText('Test text')).toBeInTheDocument();
    expect(screen.getByText('10%')).toBeInTheDocument();
  });
});
