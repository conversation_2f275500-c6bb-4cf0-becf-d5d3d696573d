import type { StorybookConfig } from '@storybook/react-native-web-vite';

const config: StorybookConfig = {
  stories: ['../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-docs',
    '@storybook/addon-themes',
    'storybook-addon-pseudo-states',
  ],
  framework: {
    name: '@storybook/react-native-web-vite',
    options: {
      builder: {
        viteConfigPath: '.storybook/vite.config.ts',
      },
      pluginReactOptions: {
        jsxRuntime: 'automatic',
        jsxImportSource: 'nativewind',
        babel: {
          presets: ['nativewind/babel'],
          plugins: [
            'react-native-web',
            '@babel/plugin-transform-export-namespace-from',
            'react-native-reanimated/plugin',
          ],
        },
      },
    },
  },
  typescript: {
    reactDocgen: 'react-docgen-typescript',
  },
};

export default config;