import React from 'react';
import { withThemeByClassName } from '@storybook/addon-themes';
import type { Preview, ReactRenderer } from '@storybook/react';
import { PortalHost, PortalProvider } from '@gorhom/portal';
import { PortalHost as RNPrimitivesPortalHost } from '@rn-primitives/portal';
import './global.css';

const preview: Preview = {
  decorators: [
    withThemeByClassName<ReactRenderer>({
      themes: {
        light: '',
        dark: 'dark',
      },
      defaultTheme: 'light',
    }),
    (Story) => (
      <PortalProvider>
        <Story />
        <PortalHost name="appPortal" />
        <RNPrimitivesPortalHost />
      </PortalProvider>
      
    ),
  ],

  tags: ['autodocs'],
};

export default preview;
