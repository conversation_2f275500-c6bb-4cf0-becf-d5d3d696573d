import * as React from 'react';
import * as AvatarPrimitive from '@rn-primitives/avatar/dist/avatar';
import { cva, type VariantProps } from 'class-variance-authority';
import { Text } from 'react-native';
import type { Svg } from 'react-native-svg';
import { cn, cssInterop } from '../utils';

const AvatarPrimitiveRoot = cssInterop(AvatarPrimitive.Root);
const AvatarPrimitiveImage = cssInterop(AvatarPrimitive.Image);
const AvatarPrimitiveFallback = cssInterop(AvatarPrimitive.Fallback);

const avatarVariants = cva('relative flex shrink-0 overflow-hidden rounded-full', {
  variants: {
    shape: {
      circle: 'rounded-full',
      square: 'rounded-md',
    },
    size: {
      xs: 'w-4 h-4',
      '5': 'w-5 h-5',
      '7': 'w-6 h-6',
      sm: 'w-7 h-7',
      md: 'w-8 h-8',
      lg: 'w-10 h-10',
      '12': 'w-12 h-12',
      '14': 'w-14 h-14',
      '16': 'w-16 h-16',
    },
  },
});

export type AvatarRootProps = React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root> &
  VariantProps<typeof avatarVariants>;

interface IRootContext extends Pick<AvatarRootProps, 'shape' | 'size'> {}

export const RootContext = React.createContext<IRootContext | null>(null);

function useRootContext() {
  const context = React.useContext(RootContext);
  if (!context) {
    throw new Error('Avatar compound components cannot be rendered outside the Avatar component');
  }
  return context;
}

export const Root = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root> & VariantProps<typeof avatarVariants>
>(({ className, shape = 'circle', size = 'md', ...props }, ref) => {
  const value = React.useMemo(() => ({ shape, size }), [shape, size]);

  return (
    <RootContext.Provider value={value}>
      <AvatarPrimitiveRoot ref={ref} className={cn(avatarVariants({ shape, size }), className)} {...props} />
    </RootContext.Provider>
  );
});
Root.displayName = AvatarPrimitive.Root.displayName;

export const Image = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Image>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>
>(({ className, ...props }, ref) => (
  <AvatarPrimitiveImage ref={ref} className={cn('aspect-square h-full w-full', className)} {...props} />
));
Image.displayName = AvatarPrimitive.Image.displayName;

export const Fallback = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Fallback>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>
>(({ className, ...props }, ref) => (
  <AvatarPrimitiveFallback
    ref={ref}
    className={cn('flex h-full w-full items-center justify-center bg-gray-400', className)}
    {...props}
  />
));
Fallback.displayName = AvatarPrimitive.Fallback.displayName;

const fallbackTextVariants = cva('leading-none font-normal text-white', {
  variants: {
    size: {
      xs: 'text-[6px]',
      '5': 'text-[6px]',
      sm: 'text-[9px]',
      '7': 'text-[10px]',
      md: 'text-xs',
      lg: 'text-sm',
      '12': 'text-base',
      '14': 'text-lg',
      '16': 'text-xl',
    },
  },
});
export const FallbackText = React.forwardRef<
  React.ElementRef<typeof Text>,
  React.ComponentPropsWithoutRef<typeof Text>
>(({ className, ...props }, ref) => {
  const { size } = useRootContext();
  return <Text ref={ref} className={cn(fallbackTextVariants({ size }), className)} {...props} />;
});

const fallbackIconVariants = cva('leading-none font-normal text-white', {
  variants: {
    size: {
      xs: 'w-[10px] h-[10px]',
      '5': 'w-3 h-3',
      sm: 'w-4 h-4',
      '7': 'w-[18px] h-[18px]',
      md: 'w-[18px] h-[18px]',
      lg: 'w-6 h-6',
      '12': 'w-7 h-7',
      '14': 'w-8 h-8',
      '16': 'w-[38px] h-[38px]',
    },
  },
});
export const FallbackIcon = React.forwardRef<
  React.ElementRef<typeof Svg>,
  React.ComponentPropsWithoutRef<typeof Svg> & { Icon: React.ElementType }
>(({ className, Icon, ...props }, ref) => {
  const { size } = useRootContext();
  return <Icon ref={ref} className={cn(fallbackIconVariants({ size }), className)} {...props} />;
});
FallbackIcon.displayName = 'Avatar.FallbackIcon';
