import type { ComponentProps } from 'react';
import { render, screen, userEvent } from '@testing-library/react-native';
import * as Select from './Select';

const people = [
  { id: 'user-1', name: '<PERSON><PERSON><PERSON>' },
  { id: 'user-2', name: '<PERSON><PERSON>' },
  { id: 'user-3', name: '<PERSON><PERSON>' },
  { id: 'user-4', name: '<PERSON>' },
  { id: 'user-5', name: '<PERSON><PERSON>' },
];

const SelectPeople = (props: ComponentProps<typeof Select.Root>) => {
  return (
    <Select.Root {...props}>
      <Select.Trigger>
        <Select.Value placeholder="Select a person" />
      </Select.Trigger>
      <Select.Content>
        {people.map((person) => (
          <Select.Item key={person.id} value={person.id} label={person.name} />
        ))}
      </Select.Content>
    </Select.Root>
  );
};

describe('<Select />', () => {
  describe('when presses the trigger', () => {
    it('opens the options list', async () => {
      render(<SelectPeople />);

      await userEvent.press(screen.getByRole('combobox', { name: 'Select a person' }));

      expect(screen.getAllByRole('option')).toHaveLength(5);
    });
  });

  it('displays the selected option in the trigger', async () => {
    render(<SelectPeople value={{ value: 'user-1', label: 'Durward Reynolds' }} />);

    expect(screen.getByRole('combobox', { name: 'Durward Reynolds' })).toBeOnTheScreen();
  });

  it('sets the option selected in the list', async () => {
    render(<SelectPeople />);

    await userEvent.press(screen.getByRole('combobox', { name: 'Select a person' }));
    await userEvent.press(screen.getByRole('option', { name: 'Kenton Towne' }));

    expect(screen.getByRole('combobox', { name: 'Kenton Towne' })).toBeOnTheScreen();
  });
});
