import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-native-web-vite';
import { cn } from '../utils';
import * as Select from './Select';
import type { Si<PERSON>, Variant } from './types';

const meta: Meta<typeof Select.Root> = {
  title: 'Form/Select',
  component: Select.Root,
  subcomponents: {
    Trigger: Select.Trigger,
    Content: Select.Content,
    Item: Select.Item,
    ItemText: Select.ItemText,
  },
};

export default meta;

type Story = StoryObj<typeof Select.Root>;

const people = [
  { id: 1, name: '<PERSON><PERSON><PERSON>', disabled: false },
  { id: 2, name: '<PERSON><PERSON>', disabled: false },
  { id: 3, name: '<PERSON><PERSON>', disabled: false },
  { id: 4, name: '<PERSON>', disabled: true },
  {
    id: 5,
    name: 'Deserunt quis est nostrud ullamco pariatur sit est nostrud est nostrud ullamco pariatur sit est nostrud',
    disabled: false,
  },
  { id: 6, name: '<PERSON>', disabled: false },
  { id: 7, name: '<PERSON>', disabled: false },
  { id: 8, name: '<PERSON>', disabled: true },
  { id: 9, name: '<PERSON>', disabled: true },
  { id: 10, name: '<PERSON>', disabled: true },
];

const pseudoStates = ['default', 'hover', 'focus', 'disabled'] as const;
const sizes: Size[] = ['md', 'sm'];
const variants: Variant[] = ['bordered', 'plain'];
const colors = ['light', 'dark'] as const;

export const Variants: Story = {
  render: () => {
    return (
      <div className="flex flex-col gap-6">
        {variants.map((variant) => (
          <div key={variant} className="w-full flex flex-col gap-4">
            {pseudoStates.map((state) => (
              <div key={state} className="flex flex-row gap-2 items-center">
                {sizes.map((size) => (
                  <div className="flex-1" key={variant + state + size}>
                    <abbr className="no-underline" title={JSON.stringify({ size, variant, state }, null, 4)}>
                      <Select.Root id={state} disabled={state === 'disabled'} size={size} variant={variant}>
                        <Select.Trigger id={state}>
                          <Select.Value placeholder="Search placeholder" /> ({variant}-{state}-{size})
                        </Select.Trigger>
                        <Select.Content>
                          {people.map((option) => (
                            <Select.Item
                              key={option.id}
                              disabled={option.disabled}
                              value={option.name}
                              label={option.name}
                            />
                          ))}
                        </Select.Content>
                      </Select.Root>
                    </abbr>
                  </div>
                ))}
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  },

  parameters: {
    pseudo: {
      focus: '#focus',
      hover: '#hover',
      disabled: '#disabled',
    },
  },
};

export const Colors = {
  render: () => {
    return (
      <div className="flex flex-col gap-6">
        {colors.map((color) => (
          <div
            key={color}
            className={cn('w-full flex flex-col gap-4 p-4', {
              'bg-gray-700': color === 'dark',
            })}
          >
            {pseudoStates.map((state) => (
              <div data-theme={color} key={state} className="flex flex-row gap-2 items-center">
                {variants.map((variant) => (
                  <div className="flex-1" key={color + state + variant}>
                    <abbr className="no-underline" title={JSON.stringify({ variant, color, state }, null, 4)}>
                      <Select.Root variant={variant} disabled={state === 'disabled'}>
                        <Select.Trigger id={state}>
                          <Select.Value placeholder="Search placeholder" />{' '}
                          <span className="text-neutral">
                            ({color}-{state}-{variant})
                          </span>
                        </Select.Trigger>
                      </Select.Root>
                    </abbr>
                  </div>
                ))}
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  },

  parameters: {
    pseudo: {
      focus: '#focus',
      hover: '#hover',
      disabled: '#disabled',
    },
  },
};
