import { type ComponentProps, createContext, type ReactNode, useContext, useMemo } from 'react';
import type { PortalProps } from '@rn-primitives/select';
import * as SelectPrimitive from '@rn-primitives/select/dist/select';
import { StyleSheet, View } from 'react-native';
import { CheckIcon, ChevronDownIcon } from '../Icons/solid';
import { cn, cssInterop } from '../utils';
import { indicatorVariants, textVariants, triggerVariants } from './select-classes';
import type { Size, Variant } from './types';

const PrimitiveTrigger = cssInterop(SelectPrimitive.Trigger);
const PrimitiveContent = cssInterop(SelectPrimitive.Content);
const PrimitiveItem = cssInterop(SelectPrimitive.Item);
const PrimitiveItemText = cssInterop(SelectPrimitive.ItemText);
const PrimitiveOverlay = cssInterop(SelectPrimitive.Overlay);
const PrimitiveValue = cssInterop(SelectPrimitive.Value);

type SelectTheme = {
  size: Size;
  variant: Variant;
};

const SelectProvider = createContext<SelectTheme>({
  size: 'md',
  variant: 'bordered',
});
const useThemeContext = () => useContext(SelectProvider);

export const Root = ({
  size = 'md',
  variant = 'bordered',
  ...props
}: ComponentProps<typeof SelectPrimitive.Root> & Partial<SelectTheme>) => {
  const value = useMemo(() => ({ size, variant }), [size, variant]);

  return (
    <SelectProvider.Provider value={value}>
      <SelectPrimitive.Root {...props} />
    </SelectProvider.Provider>
  );
};

type TriggerProps = React.ComponentProps<typeof SelectPrimitive.Trigger>;

export const Trigger = ({ children, disabled, ...props }: TriggerProps) => {
  const context = SelectPrimitive.useRootContext();
  const { size, variant } = useThemeContext();

  return (
    <PrimitiveTrigger
      {...props}
      disabled={disabled || context.disabled}
      className={cn(triggerVariants({ variant, size }), props.className)}
    >
      <>
        {children}
        <ChevronDownIcon aria-hidden className={cn(indicatorVariants({ size }), props.className)} />
      </>
    </PrimitiveTrigger>
  );
};

export const Value = (props: React.ComponentProps<typeof SelectPrimitive.Value>) => {
  const { size } = useThemeContext();
  return <PrimitiveValue {...props} numberOfLines={1} className={cn(textVariants({ size }), props.className)} />;
};

export const Content = ({
  children,
  ...props
}: React.ComponentProps<typeof PrimitiveContent> & Pick<PortalProps, 'hostName'>) => {
  return (
    <SelectPrimitive.Portal>
      <PrimitiveOverlay style={StyleSheet.absoluteFill} className="flex flex-row bg-black/40">
        <PrimitiveContent
          {...props}
          avoidCollisions
          align="start"
          side="bottom"
          position="popper"
          className={cn(
            'bg-surface-overlay mt-1.5 flex flex-col',
            'rounded-md border border-black/5 shadow-lg outline-hidden overflow-hidden',
            'divide-y divide-gray-100',
            props.className
          )}
        >
          <SelectPrimitive.ScrollUpButton />
          <SelectPrimitive.Viewport>{children}</SelectPrimitive.Viewport>
          <SelectPrimitive.ScrollDownButton />
        </PrimitiveContent>
      </PrimitiveOverlay>
    </SelectPrimitive.Portal>
  );
};

export const Item = ({ children, ...props }: React.ComponentProps<typeof SelectPrimitive.Item>) => {
  return (
    <PrimitiveItem
      {...props}
      className={cn(
        'cursor-pointer w-full p-4 gap-2.5 flex flex-row justify-start items-center',
        'aria-checked:bg-selected-subtle aria-pressed:bg-selected-focus aria-disabled:opacity-60 aria-disabled:text-neutral-bold',
        'hover:bg-selected-hovered',
        'focus:bg-selected-focus focus-visible:outline-hidden',
        props.className
      )}
    >
      <View className="flex-1">{(children as ReactNode) || <ItemText />}</View>
      <View className="flex flex-row items-center justify-center w-10">
        <SelectPrimitive.ItemIndicator>
          <CheckIcon className="h-5 w-5 text-icon-brand" />
        </SelectPrimitive.ItemIndicator>
      </View>
    </PrimitiveItem>
  );
};

export const ItemText = (props: React.ComponentProps<typeof SelectPrimitive.ItemText>) => {
  return (
    <PrimitiveItemText
      {...props}
      numberOfLines={1}
      className={cn('w-full text-neutral-bold text-sm leading-5', props.className)}
    />
  );
};
