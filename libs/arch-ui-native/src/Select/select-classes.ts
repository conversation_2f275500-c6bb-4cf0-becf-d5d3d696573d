import { cva, type VariantProps } from 'class-variance-authority';

export const triggerVariants = cva(
  [
    'group relative flex flex-row gap-2 items-center rounded-sm outline-hidden overflow-hidden min-w-0',
    'bg-input',
    'aria-disabled:opacity-50',
  ],
  {
    variants: {
      variant: {
        plain: 'focus:outline-hidden',
        bordered: [
          'border border-input aria-expanded:border-input-selected',
          'hover:border-input-selected focus:border-input-selected',
        ],
      },
      size: {
        sm: 'py-2 px-2.5 text-xs leading-4 font-normal',
        md: 'py-2.5 px-3 text-sm leading-5',
      },
    },
    defaultVariants: {
      variant: 'bordered',
      size: 'md',
    },
  }
);

export const textVariants = cva(
  'flex-1 text-neutral-bold group-hover:text-selected group-focus:text-selected group-disabled:opacity-50 truncate',
  {
    variants: {
      size: {
        sm: 'text-xs leading-4',
        md: 'text-sm leading-5',
      },
    },
    defaultVariants: {
      size: 'md',
    },
  }
);

export const indicatorVariants = cva('inline-flex center opacity-50 group-aria-expanded:rotate-180', {
  variants: {
    size: {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

// Export types for use in components
export type TriggerVariants = VariantProps<typeof triggerVariants>;
export type TextVariants = VariantProps<typeof textVariants>;
export type IndicatorVariants = VariantProps<typeof indicatorVariants>;
