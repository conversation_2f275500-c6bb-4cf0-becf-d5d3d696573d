import type { ComponentProps, ComponentType } from 'react';
import { type ClassValue, clsx } from 'clsx';
import { cssInterop as nativewindCssInterop } from 'nativewind';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function cssInterop<T extends ComponentType<any>>(
  Component: T,
  mapping: Record<string, string> = { className: 'style' }
): ComponentType<ComponentProps<T> & { className?: string }> {
  return nativewindCssInterop(Component, mapping as any);
}
