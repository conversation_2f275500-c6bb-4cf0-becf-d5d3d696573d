const { theme } = require('./src/theme');

/** @type {import('tailwindcss').Config} */
module.exports = {
  // We are currently not using dark mode, uncomment line below when we are ready.
  darkMode: ['selector', '[data-mode="dark"]'],
  presets: [require('nativewind/preset')],
  content: ['./src/**/*.{js,jsx,ts,tsx}', './libs/arch-ui-native/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: theme,
  },
};
