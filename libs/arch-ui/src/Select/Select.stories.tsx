import React, { useState } from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import classNames from 'clsx';
import Button from '../Button';
import { InputAdornment, type InputAdornmentProps } from '../InputAdornment';
import Modal from '../Modal';
import { SearchField } from '../SearchField';
import * as Select from './index';
import type { Color, Size, Variant } from './types';

export default {
  component: Select.Root,
  subcomponents: Select,
  title: 'Input/Select/Select',
} as Meta;

const people = [
  { id: 1, name: '<PERSON><PERSON><PERSON>', disabled: false },
  { id: 2, name: '<PERSON><PERSON>', disabled: false },
  { id: 3, name: '<PERSON><PERSON>', disabled: false },
  { id: 4, name: '<PERSON>', disabled: true },
  { id: 5, name: 'Deserunt quis est nostrud ullamco pariatur sit est nostrud', disabled: false },
  { id: 6, name: '<PERSON>', disabled: false },
  { id: 7, name: '<PERSON>', disabled: false },
  { id: 8, name: '<PERSON>', disabled: true },
  { id: 9, name: '<PERSON>', disabled: true },
  { id: 10, name: '<PERSON>', disabled: true },
];
type People = (typeof people)[number];

const pseudoStates = ['default', 'hover', 'focus', 'disabled'] as const;
const sizes: Size[] = ['md', 'sm', 'xs'];
const variants: Variant[] = ['bordered', 'plain'];
const colors: Color[] = ['light', 'dark'];

export const Variants = {
  render: () => {
    return (
      <div className="flex flex-col gap-6">
        {variants.map((variant) => (
          <div key={variant} className="w-full flex flex-col gap-4">
            {pseudoStates.map((state) => (
              <div key={state} className="flex flex-row gap-2 items-center">
                {sizes.map((size) => (
                  <div className="flex-1" key={variant + state + size}>
                    <abbr className="no-underline" title={JSON.stringify({ size, variant, state }, null, 4)}>
                      <Select.Root disabled={state === 'disabled'} data-id={state}>
                        <Select.Trigger size={size} data-id={state} variant={variant}>
                          <Select.Value placeholder="Search placeholder" value={null} />
                        </Select.Trigger>
                      </Select.Root>
                    </abbr>
                  </div>
                ))}
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  },

  parameters: {
    pseudo: {
      focus: '[data-id="focus"]',
      hover: '[data-id="hover"]',
      disabled: '[data-id="disabled"]',
    },
  },
};

export const Colors = {
  render: () => {
    return (
      <div className="flex flex-col gap-6">
        {colors.map((color) => (
          <div
            key={color}
            className={classNames('w-full flex flex-col gap-4 p-4', {
              'bg-gray-700': color === 'dark',
            })}
          >
            {pseudoStates.map((state) => (
              <div key={state} className="flex flex-row gap-2 items-center">
                {variants.map((variant) => (
                  <div className="flex-1" key={color + state + variant}>
                    <abbr className="no-underline" title={JSON.stringify({ variant, color, state }, null, 4)}>
                      <Select.Root disabled={state === 'disabled'} data-id={state}>
                        <Select.Trigger variant={variant} data-id={state} color={color}>
                          <Select.Value placeholder="Search placeholder" value={null} />
                        </Select.Trigger>
                      </Select.Root>
                    </abbr>
                  </div>
                ))}
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  },

  parameters: {
    pseudo: {
      focus: '[data-id="focus"]',
      hover: '[data-id="hover"]',
      disabled: '[data-id="disabled"]',
    },
  },
};

export const Layout: StoryFn = () => {
  const [selectedPerson, setSelectedPerson] = useState<People['name'] | null>(null);

  return (
    <div className="flex flex-col gap-4 md:items-start">
      <div>
        <h1>With disabled options</h1>
        <Select.Root value={selectedPerson} onChange={setSelectedPerson}>
          <Select.Trigger variant="bordered" startAdornment={<InputAdornment>Person</InputAdornment>}>
            <Select.Value placeholder="Select placeholder" value={selectedPerson} />
          </Select.Trigger>
          <Select.Panel>
            <Select.Options>
              {people.map((person) => (
                <Select.Option key={person.id} value={person.name} disabled={person.disabled}>
                  <Select.OptionText>
                    {person.id} - {person.name}
                  </Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.Panel>
        </Select.Root>
      </div>
      <div>
        <h1>Default</h1>
        <Select.Root value={selectedPerson} onChange={setSelectedPerson}>
          <Select.Trigger variant="bordered" startAdornment={<InputAdornment>Person</InputAdornment>}>
            <Select.Value placeholder="Select placeholder" value={selectedPerson} />
          </Select.Trigger>
          <Select.Panel>
            <Select.Options>
              {people.map((person) => (
                <Select.Option key={person.id} value={person.name}>
                  <Select.OptionText>
                    {person.id} - {person.name}
                  </Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.Panel>
        </Select.Root>
      </div>
      <div>
        <h1>Responsive</h1>
        <Select.Root value={selectedPerson} onChange={setSelectedPerson}>
          <Select.Trigger variant="bordered" startAdornment={<InputAdornment>Person</InputAdornment>}>
            <Select.Value placeholder="Select placeholder" value={selectedPerson} />
          </Select.Trigger>
          <Select.ResponsivePanel>
            <Select.Options>
              {people.map((person) => (
                <Select.Option key={person.id} value={person.name}>
                  <Select.OptionText>
                    {person.id} - {person.name}
                  </Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.ResponsivePanel>
        </Select.Root>
      </div>
    </div>
  );
};

export const PositionAdjustment: StoryFn = () => {
  const [selectedPerson, setSelectedPerson] = useState<People['name'] | null>(null);

  return (
    <div className="flex gap-8 justify-between flex-wrap">
      <Select.Root value={selectedPerson} onChange={setSelectedPerson}>
        <Select.Trigger className="w-60" variant="bordered" startAdornment={<InputAdornment>Person</InputAdornment>}>
          <Select.Value placeholder="Select placeholder" value={selectedPerson} />
        </Select.Trigger>
        <Select.ResponsivePanel className="md:w-96" placement="bottom-start">
          <Select.Options>
            {people.map((person) => (
              <Select.Option key={person.id} value={person.name}>
                <Select.OptionText>
                  {person.id} - {person.name}
                </Select.OptionText>
              </Select.Option>
            ))}
          </Select.Options>
        </Select.ResponsivePanel>
      </Select.Root>
      <Select.Root value={selectedPerson} onChange={setSelectedPerson}>
        <Select.Trigger className="w-60" variant="bordered" startAdornment={<InputAdornment>Person</InputAdornment>}>
          <Select.Value placeholder="Select placeholder" value={selectedPerson} />
        </Select.Trigger>
        <Select.ResponsivePanel className="md:w-96" placement="bottom-end">
          <Select.Options>
            {people.map((person) => (
              <Select.Option key={person.id} value={person.name}>
                <Select.OptionText>
                  {person.id} - {person.name}
                </Select.OptionText>
              </Select.Option>
            ))}
          </Select.Options>
        </Select.ResponsivePanel>
      </Select.Root>
    </div>
  );
};

export const MultiSelect: StoryFn = () => {
  const [selectedPerson, setSelectedPerson] = useState<People[]>(people);

  return (
    <div className="flex flex-col gap-4">
      <div>
        <h1>Default</h1>
        <Select.Root value={selectedPerson} onChange={setSelectedPerson} multiple>
          <Select.Trigger variant="bordered">
            <Select.MultipleValue
              placeholder="Select placeholder"
              value={selectedPerson}
              displayValue={({ name }) => name}
            />
          </Select.Trigger>
          <Select.Panel>
            <Select.Options>
              {people.map((person) => (
                <Select.Option key={person.id} value={person}>
                  <Select.OptionText>
                    {person.id} - {person.name}
                  </Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.Panel>
        </Select.Root>
      </div>

      <div>
        <h1>With limit selected value</h1>
        <Select.Root value={selectedPerson} onChange={setSelectedPerson} multiple>
          <Select.Trigger variant="bordered">
            <Select.MultipleValue
              placeholder="Select placeholder"
              value={selectedPerson}
              displayValue={({ name }) => name}
              limit={2}
            />
          </Select.Trigger>
          <Select.Panel>
            <Select.Options>
              {people.map((person) => (
                <Select.Option key={person.id} value={person.name}>
                  <Select.OptionText>
                    {person.id} - {person.name}
                  </Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.Panel>
        </Select.Root>
      </div>

      <div>
        <h1>Condensed</h1>
        <Select.Root value={selectedPerson} onChange={setSelectedPerson} multiple>
          <Select.Trigger variant="bordered">
            <Select.MultipleValue placeholder="Select placeholder" value={selectedPerson} condensed />
          </Select.Trigger>
          <Select.Panel>
            <Select.Options>
              {people.map((person) => (
                <Select.Option key={person.id} value={person.name}>
                  <Select.OptionText>
                    {person.id} - {person.name}
                  </Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.Panel>
        </Select.Root>
      </div>

      <div>
        <h1>With remove</h1>
        <Select.Root value={selectedPerson} onChange={setSelectedPerson} multiple>
          <Select.Trigger variant="bordered">
            <Select.MultipleValue
              placeholder="Select placeholder"
              value={selectedPerson}
              displayValue={(person) => person.name}
              onRemove={(selected) => {
                setSelectedPerson((state) => state.filter((person) => person !== selected));
              }}
              onRemoveAll={() => setSelectedPerson([])}
            />
          </Select.Trigger>
          <Select.Panel>
            <Select.Options>
              {people.map((person) => (
                <Select.Option key={person.id} value={person.name}>
                  <Select.OptionText>
                    {person.id} - {person.name}
                  </Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.Panel>
        </Select.Root>
      </div>
    </div>
  );
};

export const Panels: StoryFn = () => {
  const [selectedPerson, setSelectedPerson] = useState<People['name'][]>([]);
  const [search, setSearch] = useState('');
  const filtered = people.filter(({ name }) => name.includes(search));
  return (
    <Select.Root value={selectedPerson} onChange={setSelectedPerson} multiple>
      <Select.Trigger variant="bordered">
        <Select.MultipleValue placeholder="Select placeholder" value={selectedPerson} condensed />
      </Select.Trigger>
      <Select.ResponsivePanel>
        <Select.PanelSection>
          <SearchField onChange={(event) => setSearch(event.target.value)} />
        </Select.PanelSection>
        {selectedPerson.length > 0 && (
          <Select.PanelSection>
            <Select.MultipleValue
              value={selectedPerson}
              onRemove={(selected) => {
                setSelectedPerson((state) => state.filter((person) => person !== selected));
              }}
              onRemoveAll={() => setSelectedPerson([])}
            />
          </Select.PanelSection>
        )}
        <Select.Options>
          {filtered.map((person) => (
            <Select.Option key={person.id} value={person.name}>
              <Select.OptionText>
                {person.id} - {person.name}
              </Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};

export const Portal: StoryFn = () => {
  const [selectedPerson, setSelectedPerson] = useState<People['name']>();
  const onClose = () => {};
  return (
    <Modal.Root open onClose={onClose}>
      <Modal.Header onClose={onClose}>
        <Modal.Title>Title</Modal.Title>
      </Modal.Header>
      <Modal.Content className="p-4">
        <Select.Root value={selectedPerson} onChange={setSelectedPerson}>
          <Select.Trigger variant="bordered">
            <Select.Value placeholder="Select placeholder" value={selectedPerson!} />
          </Select.Trigger>
          <Select.ResponsivePanel portal>
            <Select.Options>
              {people.map((person) => (
                <Select.Option key={person.id} value={person.name}>
                  <Select.OptionText>
                    {person.id} - {person.name}
                  </Select.OptionText>
                </Select.Option>
              ))}
            </Select.Options>
          </Select.ResponsivePanel>
        </Select.Root>
      </Modal.Content>
      <Modal.Footer>
        <Button color="secondary" variant="contained" size="md" type="submit" onClick={onClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal.Root>
  );
};

export const Adornments: StoryFn = () => {
  const adornmentRepresentations: {
    size: Size;
    variant: InputAdornmentProps['variant'];
    position: InputAdornmentProps['position'];
  }[] = [
    {
      size: 'sm',
      variant: 'detached',
      position: 'start',
    },
    {
      size: 'md',
      variant: 'detached',
      position: 'start',
    },
    {
      size: 'sm',
      variant: 'inline',
      position: 'start',
    },
    {
      size: 'md',
      variant: 'inline',
      position: 'start',
    },
    {
      size: 'sm',
      variant: 'detached',
      position: 'end',
    },
    {
      size: 'md',
      variant: 'detached',
      position: 'end',
    },
    {
      size: 'sm',
      variant: 'inline',
      position: 'end',
    },
    {
      size: 'md',
      variant: 'inline',
      position: 'end',
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      {adornmentRepresentations.map(({ size, position, variant }) => {
        const positionElement = position === 'start' ? 'startAdornment' : 'endAdornment';
        return (
          <Select.Root key={size + position + variant}>
            <Select.Trigger
              size={size}
              variant="bordered"
              {...{
                [positionElement]: (
                  <InputAdornment variant={variant} position={position}>
                    text
                  </InputAdornment>
                ),
              }}
            >
              <Select.Value placeholder="Search placeholder" value={null} />
            </Select.Trigger>
          </Select.Root>
        );
      })}
    </div>
  );
};
