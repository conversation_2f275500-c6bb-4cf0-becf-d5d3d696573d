// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { TeamSubscriptionPlanSchema } from '../types/teamSubscriptionPlanSchema';
import { createTeamSubscriptionPlanQuotaFeature } from './createTeamSubscriptionPlanQuotaFeature';
import { createTeamSubscriptionPlanTimespanFeature } from './createTeamSubscriptionPlanTimespanFeature';
import { createTeamSubscriptionPlanToggleFeature } from './createTeamSubscriptionPlanToggleFeature';
import { faker } from '@faker-js/faker';

export function createTeamSubscriptionPlan(data?: Partial<TeamSubscriptionPlanSchema>): TeamSubscriptionPlanSchema {
  faker.seed([100]);
  return {
    ...{
      activePlanName: faker.string.alpha(),
      activePlanSlug: faker.string.alpha(),
      billing: { costPerUserInCents: faker.number.int(), currency: faker.string.alpha() },
      features: {
        changeTracker: createTeamSubscriptionPlanToggleFeature(),
        customDashboards: createTeamSubscriptionPlanToggleFeature(),
        documentReferences: createTeamSubscriptionPlanToggleFeature(),
        exportIssues: createTeamSubscriptionPlanToggleFeature(),
        exportShiftReportsData: createTeamSubscriptionPlanToggleFeature(),
        issueHistory: createTeamSubscriptionPlanTimespanFeature(),
        issuePrivateChat: createTeamSubscriptionPlanToggleFeature(),
        printIssue: createTeamSubscriptionPlanToggleFeature(),
        proDashboards: createTeamSubscriptionPlanToggleFeature(),
        projectTimeline: createTeamSubscriptionPlanTimespanFeature(),
        shiftReportsManagerView: createTeamSubscriptionPlanToggleFeature(),
        storageBytes: createTeamSubscriptionPlanQuotaFeature(),
        usersPerTeam: createTeamSubscriptionPlanQuotaFeature(),
      },
      subscribedPlanBillable: faker.datatype.boolean(),
      subscribedPlanName: faker.string.alpha(),
      subscribedPlanSlug: faker.string.alpha(),
      trialEndAt: faker.date.anytime().toISOString(),
      availableActions: {
        accessBillingPortal: faker.datatype.boolean(),
        billingPortal: faker.datatype.boolean(),
        edit: faker.datatype.boolean(),
      },
    },
    ...(data || {}),
  };
}
