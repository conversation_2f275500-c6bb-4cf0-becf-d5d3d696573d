/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const shiftReportBasicDetailsStateEnum = {
  draft: 'draft',
  in_review: 'in_review',
  published: 'published',
} as const;

export type ShiftReportBasicDetailsStateEnumSchema =
  (typeof shiftReportBasicDetailsStateEnum)[keyof typeof shiftReportBasicDetailsStateEnum];

export type ShiftReportBasicDetailsSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type integer
   */
  approverId: number | null;
  /**
   * @type boolean
   */
  archived: boolean;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    archive: boolean;
    /**
     * @type boolean
     */
    delete: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
    /**
     * @type boolean | undefined
     */
    editRootFields?: boolean;
    /**
     * @type boolean
     */
    export: boolean;
    /**
     * @type boolean
     */
    listCollaboratorsComments: boolean;
    /**
     * @type boolean
     */
    listPublicComments: boolean;
    /**
     * @type boolean
     */
    publish: boolean;
    /**
     * @type boolean
     */
    restore: boolean;
    /**
     * @type boolean
     */
    submitForReview: boolean;
  };
  /**
   * @type array
   */
  collaboratorsTeamMemberIds: number[];
  /**
   * @type integer
   */
  completionQualityScore: number | null;
  /**
   * @type string
   */
  contractorName: string | null;
  /**
   * @description Only visible to the shift report author
   * @type string | undefined, date-time
   */
  createdAt?: string;
  /**
   * @type integer
   */
  documentCount: number | null;
  /**
   * @type string, uuid
   */
  projectId: string;
  /**
   * @type string, date-time
   */
  publishedAt: string | null;
  /**
   * @type integer
   */
  publishedById: number | null;
  /**
   * @type string, date
   */
  reportDate: string;
  /**
   * @type string
   */
  reportTitle: string | null;
  /**
   * @type string
   */
  shiftType: string | null;
  /**
   * @type string
   */
  state: ShiftReportBasicDetailsStateEnumSchema;
  /**
   * @type string, uuid
   */
  teamId: string;
  /**
   * @type integer
   */
  teamMemberId: number;
};
